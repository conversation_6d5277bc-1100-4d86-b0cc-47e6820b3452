/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import defaultSettings from '../defaultSettings';
import config from '../config';
import { getOptByShopId } from '@/utils/getLocationParams';
import { setIcon } from '@/utils/utils';

export default {
  namespace: 'setting',
  state: {
    ...defaultSettings,
    ...config,
  },

  effects: {
    // 根据用户信息动态更新options配置
    *updateOptionsByUserInfo({ payload }, { put }) {
      const { shop_id } = payload || {};
      if (!shop_id) return;

      // 获取当前配置
      // const currentState = yield select(state => state.setting);

      // 尝试根据shop_id获取新的配置
      const shopIdConfig = getOptByShopId(config.options.__configMap__, shop_id);

      if (shopIdConfig) {
        // 如果找到了shop_id对应的配置，更新options
        const newOptions = {
          ...config.options.__defaultConfig__,
          ...shopIdConfig,
        };
        setIcon(newOptions.favicon);
        yield put({
          type: 'save',
          payload: {
            options: newOptions,
          },
        });
      }
    },
  },

  reducers: {
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
