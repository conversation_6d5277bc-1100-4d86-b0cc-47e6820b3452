/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { message } from 'antd';
import { setAuthority } from '@/utils/authority';
import { reloadAuthorized } from '@/utils/Authorized';
import { getCookie, getLStorage } from '@/utils/utils';
import { setCompanyInfo, getCompanyInfo, getBrandInfo } from '../services/user';
import { checkIsWxWork } from '@/utils/navigator';
import { subRoles } from '@/components/_utils/config';

const noop = () => {};
export default {
  namespace: 'info',
  state: {
    status: 'loading',
    companyData: null,
    brandLists: null,
  },
  effects: {
    // 注册Assignment to function parameter 'payload'
    *submit({ payload, then = noop }, { call, select }) {
      const { companyData } = yield select(_ => _.info);
      let porps = {};
      if (companyData) {
        const { id } = companyData;
        porps = { ...payload, id };
      } else {
        porps = { ...payload };
      }
      const companyDatas = { ...porps };
      const response = yield call(setCompanyInfo, companyDatas);
      if (!response) return;
      const { code, data, msg } = response;
      if (code == 0 && data) {
        message.success('更新成功', 0.5, () => {
          then(payload);
        });
      } else {
        message.error(msg);
      }
    },
    *getInfo({ payload, then = () => {} }, { call, put, select }) {
      let quickLoginType =
        process.env.NODE_ENV === 'development'
          ? 3
          : Number(getCookie('5376912e6b8535b3ee565675312e8968') || 0);
      let { companyData } = yield select(_ => _.info);
      let status = 'show';
      if (!payload || payload.source != 'correct') {
        if (companyData) {
          yield put({
            type: 'save',
            payload: {
              status,
            },
          });
          return;
        }
      }
      const response = yield call(getCompanyInfo, { ...payload });
      if (!response) return;
      const { code, data } = response;
      if (code == 0 && data) {
        let { roles } = data;
        const { sub_privilege } = data;
        // const shop_id = data?.user_info?.shop_id || data?.shop_id;
        const shop_id = data?.shop_id;
        const ytAuthorityList = [
          'workOrder',
          'post/advanced',
          'post/dispat',
          'post/complantBlackList',
          'post/express',
          'post/export',
          'post/bigData',
          'post/setup',
          'post/kdg-fee',
          'truck',
        ];
        const retransmit = 'post/retransmit';
        const retransmitIds = ['2397', '8445', '8143', '8116'];
        if (Array.isArray(roles) && roles.length) {
          // 圆通定制，非接口控制权限
          roles.push(...ytAuthorityList);
          if (!retransmitIds.includes(`${shop_id}`)) {
            roles.push(retransmit);
          }
        }
        if (Array.isArray(sub_privilege) && sub_privilege.length) {
          sub_privilege.push(...ytAuthorityList);
          if (!retransmitIds.includes(`${shop_id}`)) {
            sub_privilege.push(retransmit);
          }
        }
        const authorityList = [];
        // const authorityList = Array.from({ length: 999 }).map((_, index) => `${index}-1`);
        const ytShopIds = ['4577', '9581', '9930'];
        if (ytShopIds.includes(`${shop_id}`)) {
          // Tower 任务: 0302 圆通快递柜项目，前期定制需求 ( https://tower.im/teams/258300/todos/110709 )
          // 测试环境 正式环境
          roles = ['post', 'finance', 'system', 'account'];
          authorityList.push(
            ...['yt_custom', '24-0', '25-0', '26-0', '27-0', '28-0', '29-0', '30-0'],
          );
          if (checkIsWxWork()) {
            roles.push('mixInStorage');
            quickLoginType = 3;
          }
        } else {
          authorityList.push(...['not_yt_custom']);
        }
        companyData = data;
        const {
          brand_name,
          // roles,
          pid,
          phone,
          parent_phone,
          user_info = {},
          is_special,
          disclaimer_status,
          id,
          area_ids,
          gp_area_ids,
          inn_area_ids,
          is_sitescan,
          is_sf_id = (process.env.DEBUG_DEV === 'OPEN'
            ? ['***********']
            : ['***********', '***********', '***********']
          ).includes(phone)
            ? 1
            : 0,
          is_trace_code = process.env.DEBUG_DEV === 'OPEN' ? 1 : 0,
          biz_modules,
          biz_menus,
          is_multiSelect = process.env.NODE_ENV === 'development' ? true : true,
          // : ['22', '8778'].includes(`${shop_id}`)),
          sub_privilege_item = '',
          charge_notice = false,
        } = data;
        const currentUser = {
          shop_id,
          phone,
          parent_phone,
          name: brand_name,
          roles,
          sub_privilege,
          pid,
          user_info: {
            area_ids,
            gp_area_ids: gp_area_ids || [],
            inn_area_ids: inn_area_ids || [],
            id,
            ...user_info,
          },
          is_special: is_special == 1, // 是否是特许加盟商
          is_sitescan, // 是否开启场地扫码权限
          disclaimer_status, // 同意使用共配，1： 是， 0：否
          id,
          quickLoginType, // 快捷登录类型，控制代入库，不存在或者0 不展示   1 仅展示查询   2 查询和新增查看详情  3查询新增和导入
          is_sf_id, //  添加业务员是否需要骑士ID，1：是，0f否
          is_trace_code, // 邮政速递新增配置项“道段编号”
          biz_modules, // 使用的业务
          biz_menus,
          is_multiSelect,
          is_preventError: true, // 四段码，中通申通防错分
          sub_privilege_item,
          charge_notice,
        };

        const _authorityList = sub_privilege_item.split(',').filter(Boolean);

        // 未设置过快递柜权限的 注入kdg相关特殊权限
        if (JSON.stringify(_authorityList).indexOf('kdg_') == -1) {
          const kdgRole = subRoles.Cabinet.map(item => `${item.id}-2`);
          _authorityList.push(...kdgRole);
        }
        authorityList.push(..._authorityList);

        // 递先锋
        if (shop_id == '4601') {
          authorityList.push(...['workOrder-0', 'fund-0']);
        }

        currentUser.authorityList = authorityList;

        yield put({
          type: 'user/saveCurrentUser',
          payload: {
            currentUser,
          },
        });

        // 根据用户的shop_id动态更新options配置
        yield put({
          type: 'setting/updateOptionsByUserInfo',
          payload: {
            shop_id,
          },
        });
        const cacheAreaId = getLStorage(`KB_INN_AREA_${currentUser.user_info.phone}`);
        const isCompany = area_ids == '*';
        // 防止片区被关闭，导致请求带上上一个的片区ID
        const area_id = currentUser.user_info.inn_area_ids.includes(cacheAreaId)
          ? cacheAreaId
          : isCompany
            ? '0'
            : currentUser.user_info.inn_area_ids.join(',');
        yield put({
          type: 'area/changeArea',
          payload: {
            area_id,
          },
        });
        setAuthority({
          isRoot: pid == 0,
          root: roles,
          sub: sub_privilege.filter(item => item !== '*'),
          currentUser,
        });
        reloadAuthorized();
        then(currentUser);
        if (!brand_name || !phone) {
          status = 'editor';
        } else {
          status = 'show';
        }
      } else {
        status = 'editor';
        companyData = null;
      }
      yield put({
        type: 'save',
        payload: {
          status,
          companyData,
        },
      });
      return response;
    },
    *switchStatus({ payload }, { put }) {
      yield put({
        type: 'save',
        payload: {
          status: payload.status,
        },
      });
    },
    *getBrandInfo({ payload }, { call, put }) {
      const response = yield call(getBrandInfo, { ...payload });
      if (!response) return;
      const { code } = response;
      if (code == 0 && response.data) {
        yield put({
          type: 'save',
          payload: {
            brandLists: response.data,
          },
        });
      }
    },
  },

  reducers: {
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
