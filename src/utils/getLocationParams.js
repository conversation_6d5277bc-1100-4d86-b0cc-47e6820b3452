/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { isNaN } from 'lodash';
import { setStorageSync, getStorageSync } from './storage';

export const getLocationParams = () => {
  const { search } = window.location;
  return Object.fromEntries(new URLSearchParams(search));
};
/**
 *   http://localhost:8000/system/business/client?__host__=gp.chinapostcnps.com
 *  从路径中获取host 仅在本地有效  host会放在 sessionStorage 中
 * @returns
 */
const storageType = 'session';
const hostKey = '__host__';
export const getOptByHost = target => {
  if (process.env.NODE_ENV !== 'development') {
    return target[window.location.hostname];
  }

  const { __host__ } = getLocationParams();
  if (__host__) {
    setStorageSync(hostKey, { [hostKey]: __host__ }, storageType);
  }
  const storage = getStorageSync(hostKey, storageType) || {};
  const data = storage.data ?? {};
  return target[__host__] || target[data[hostKey]];
};

/**
 * 根据shop_id获取配置（用于动态更新）
 * @param {Object} target - 配置对象
 * @param {number|string} shopId - 商店ID
 * @returns {Object} - 匹配的配置
 */
export const getOptByShopId = (target, shopId) => {
  if (!shopId) return null;

  // 如果shopId不包含点，认为是shop_id控制
  if (!String(shopId).includes('.')) {
    const shopIdNum = parseInt(shopId, 10);
    if (!isNaN(shopIdNum) && target[shopIdNum]) {
      return target[shopIdNum];
    }
  }

  return null;
};
