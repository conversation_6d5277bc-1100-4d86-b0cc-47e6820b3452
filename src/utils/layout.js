/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable no-plusplus */
/**
 *
 * @description 修正补充菜单相关，兼容新零售与中邮等项目
 * @param {*} menus
 * @param {*} opts
 * @param {*} opts.branchLevel -0：总公司、1：省、2：市、3：区县、4：支局
 * @returns
 */
export function fixMenusByHost(menus, opts) {
  const { key, subKey, postName, allocationName } = opts || {};

  menus = menus.map(item => {
    if (item.hideByKey?.includes(key) || item.hideByKey?.includes(subKey)) {
      item.hideInMenu = true;
    }
    item.children = (Array.isArray(item.children) ? item.children : []).map(child => {
      if (child.hideByKey?.includes(key) || child.hideByKey?.includes(subKey)) {
        child.hideInMenu = true;
      }
      return child;
    });
    if (item.children.length && item.children.every(child => child.hideInMenu)) {
      item.hideInMenu = true;
    }
    if (item.path == '/post') {
      item.name = postName + '驿站';
    }
    if (item.path == '/Allocation') {
      if (allocationName) {
        item.name = allocationName;
      } else {
        item.name = postName + '共配';
      }
    }
    if (item.path == '/business') {
      item.name = postName + '同城';
    }
    return item;
  });
  return menus;
}
