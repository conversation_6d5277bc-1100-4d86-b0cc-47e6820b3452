/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { useCallback, useRef, useState, useEffect } from "react";

// 检查标签名
function checkTagName(target, name) {
  return `${name}`.split(",").includes(target.tagName.toLowerCase());
}

// 当前元素内部是否包含指定的元素
function isContains(current, node) {
  while (node !== void 0 && node !== null && !checkTagName(node, "body")) {
    if (node === current) {
      return true;
    }
    node = node.parentNode;
  }
  return false;
}

// 查找符合条件的node
function getTargetNode(node) {
  const { overflowX } = (node && node.style) || {};
  if (overflowX === "scroll" || overflowX === "auto") {
    return node;
  }
  const childNodes = node.childNodes;
  if (childNodes) {
    for (let i = 0, len = childNodes.length; i < len; i++) {
      const current = getTargetNode(childNodes[i]);
      if (current) {
        return current;
      }
    }
  }
}

// 鼠标控制横向滚动处理组件
const MouseScroll = props => {
  // 更新鼠标样式
  const [cursor, updateCursor] = useState("default");
  const dragRef = useRef();
  // 表格refer
  const tableRef = useRef();
  let startX = -1;

  // 先获取指定的node
  useEffect(
    () => {
      if (!tableRef.current) {
        tableRef.current = getTargetNode(dragRef.current);
      }
    },
    [dragRef, tableRef]
  );

  // 监听鼠标事件
  const handleMouse = useCallback(e => {
    const { type, pageX, target } = e;
    if (!isContains(dragRef.current, target)) {
      return;
    }
    e.stopPropagation();
    if (checkTagName(target, "th")) {
      e.preventDefault();
    }
    switch (type) {
      case "mousedown":
        if (!checkTagName(target, "td,th")) {
          return;
        }
        startX = pageX;
        updateCursor("move");
        break;
      case "mousemove":
        if (startX > -1) {
          tableRef.current.scrollLeft += startX - pageX;
          startX = pageX;
        }
        break;
      default:
        startX = -1;
        updateCursor("default");
        break;
    }
  }, []);
  return (
    <div
      style={{ cursor }}
      onMouseDown={handleMouse}
      onMouseMove={handleMouse}
      onMouseUp={handleMouse}
      onMouseLeave={handleMouse}
      ref={dragRef}
    >
      {props.children}
    </div>
  );
};

const DragTable = props => {
  return React.Children.map(props.children, child => {
    const { props: { scroll: { x: scrollX } = {} } = {} } = child;
    return scrollX ? <MouseScroll>{child}</MouseScroll> : child;
  });
};

export default DragTable;
