/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React from 'react';
import { Row, Col } from 'antd';
import { useSelector } from 'dva';

const HelpAndService = () => {
  const { hideHelpAndService } = useSelector(_ => _.setting.options);
  if (hideHelpAndService) return null;
  return (
    <Row type="flex" gutter={[20, 0]}>
      <Col>
        <a target="_blank" rel="noreferrer" href="https://kuaidihelp.com/">
          帮助文档
        </a>
      </Col>
      <Col>
        <a
          target="_blank"
          rel="noreferrer"
          href="https://m.kuaidihelp.com/Home/index?platform=wkd_mini-2#/customer"
        >
          在线客服
        </a>
      </Col>
    </Row>
  );
};
export default HelpAndService;
