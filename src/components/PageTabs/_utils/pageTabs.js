/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useHistory, useDispatch, useLocation, useSelector } from 'dva';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useStorage } from '@/utils/storage';
import { isArray } from 'lodash';
import qs from 'qs';

export const PAGE_TABS = 'PAGE_TABS';

// 展开菜单
function expandRoutes(routes) {
  return routes
    .map(({ routes: routesChildren, ...item }) =>
      [item].concat(
        (routesChildren || []).map(iitem => ({
          pname: getPathNameByConfig(item.path, item.name),
          ...iitem,
        })),
      ),
    )
    .reduce((a, b) => a.concat(b));
}

// 提取pathname，过滤掉search
export function pickupPathname(path) {
  const [pathname] = `${path}`.split('?');
  return pathname;
}

/**
 * @description 修补pathname
 */
export function usePatchPathname() {
  const { pathname, search } = useLocation();
  const formatSearch = search ? `?${search.replace(/^\?/, '')}` : '';

  // 检查是否激活:exact精确：链接绝对相等
  function check(itemPath, path, exact = true) {
    return isArray(path)
      ? path.some(item => check(itemPath, item, exact))
      : exact
        ? itemPath === path
        : `${path}`.split('?')[0] === itemPath;
  }

  // 名称补丁
  function patchName(item) {
    const { name, path } = item;
    const [, suffix] = `${path}`.split('?');
    if (!suffix) return [name];
    const { tabname } = qs.parse(suffix);
    return [name, tabname ? ` - ${tabname}` : `?${decodeURIComponent(suffix)}`];
  }

  return {
    path: (pathname || '') + formatSearch,
    check,
    patchName,
  };
}

/**
 *
 * @description 记录tab
 * @param routes
 * @returns
 */
export function useRecordTab(routes) {
  const {
    data: { data: storageData },
    update: updateStorage,
  } = useStorage(PAGE_TABS);

  const history = useHistory();

  const storageTabs = (storageData || []).map(item => ({
    ...item,
    // eslint-disable-next-line react-hooks/rules-of-hooks
    pname: getPathNameByConfig(item.path, item.pname),
  }));
  const [tabs, setTabs] = useState(storageTabs);
  const [activeKey, setActiveKey] = useState('');
  const actionRef = useRef({ tabs: storageTabs });
  const dispatch = useDispatch();
  const { path: patchedPath, check: checkActive } = usePatchPathname();

  // 拉平路由配置
  const routesFormat = expandRoutes(routes);

  /**
   *
   * @description 记录并设置state.tabs
   * @param newTabs
   */
  const recordAndSetTabs = newTabs => {
    setTabs([...newTabs]);
    updateStorage(newTabs);
    actionRef.current.tabs = newTabs;
  };

  const updateTabs = (type, path) => {
    const newTabs = patchedTabs.filter(item => !checkActive(item.path, path));
    const index = patchedTabs.findIndex(item => item.path === path);
    const { path: currentPath } = patchedTabs[index] || {};
    const { path: nearPath } = newTabs[index === -1 ? newTabs.length - 1 : 0] || {};

    switch (type) {
      case 'remove':
        recordAndSetTabs(newTabs);
        if (checkActive(activeKey, path)) {
          // 关闭当前打开的标签
          updateTabs('change', nearPath);
        }
        break;
      case 'change':
        history.push(currentPath || '/');

        break;

      default:
        break;
    }
  };

  useEffect(
    () => {
      const { path, ...restItem } =
        routesFormat.find(item => checkActive(item.path, patchedPath, false)) || {};
      if (!path) {
        setActiveKey(patchedPath);
        return;
      }
      const { tabs: currentTabs } = actionRef.current;
      if (!currentTabs.some(item => checkActive(item.path, patchedPath))) {
        currentTabs.push({ path: patchedPath, ...restItem });
        recordAndSetTabs(currentTabs);
      }
      setActiveKey(patchedPath);
      dispatch({
        type: 'global/changePageTabs',
        payload: patchedPath,
      });
    },
    [patchedPath],
  );

  const patchedTabs = useMemo(() => tabs.filter(item => item.name), [tabs]);

  return [patchedTabs, activeKey, updateTabs];
}

export function usePageTabsChange() {
  const [show, setShow] = useState(false);
  const location = useLocation();
  const { pathname } = location;
  const { pathname: globalPathname } = useSelector(state => state.global);
  useEffect(
    () => {
      setShow(pathname === globalPathname);
    },
    [globalPathname],
  );
  return [show];
}

// 修补第三方定制tab文案显示
export function getPathNameByConfig(path, pname = '') {
  const { allocationName, postName } = window?.g_app?._store?.getState()?.setting?.options ?? {};
  return (
    (`${path}`.startsWith('/Allocation') && allocationName) ||
    `${pname || ''}`.replace('快宝', postName)
  );
}
