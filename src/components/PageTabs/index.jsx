/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable react/jsx-no-bind */

import React from 'react';
import { Tabs } from 'antd';
import { useSelector } from 'dva';
import Link from 'umi/link';
import Redirect from 'umi/redirect';
import AuthorizedNoMatch from '@/utils/authority';
import Authorized from '@/utils/Authorized';
import { isArray } from 'lodash';
import classNames from 'classnames';
import styles from './index.less';
import ContextMenu from './context-menu';
import { closeTabs } from './context-menu/_utils';
import { pickupPathname, usePatchPathname, useRecordTab } from './_utils/pageTabs';

const { TabPane } = Tabs;

const PageTabs = props => {
  const { route, childrenMap, checkAuthority, matchedRedirectPath } = props;
  const { collapsed } = useSelector(state => state.global);
  const [tabs, activeKey, updateTabs] = useRecordTab(route.routes);

  const handleEdit = path => updateTabs('remove', path);
  const handleChange = path => updateTabs('change', path);

  const handleOperate = (index, action) => closeTabs(index, action, tabs, handleEdit);

  const hasTabsMoreOne = isArray(tabs) && tabs.length > 1;

  const rootCls = classNames(styles.tab, {
    [styles['tab-collapsed']]: collapsed,
  });

  const { patchName } = usePatchPathname();

  // 检查权限
  const handleCheckAuthority = (currentPath, currentAuthority) =>
    checkAuthority(currentAuthority, null, false, pickupPathname(currentPath));

  return (
    <div className={rootCls}>
      <Tabs
        type="editable-card"
        hideAdd
        activeKey={activeKey}
        onEdit={handleEdit}
        onChange={handleChange}
      >
        {tabs?.map((item, index) => {
          const [name, suffix] = patchName(item);
          return (
            <TabPane
              tab={
                <ContextMenu disabled={!hasTabsMoreOne} onOperate={handleOperate.bind(null, index)}>
                  <span className="kb-tab-pname">{item.pname}</span>
                  {name}
                  {suffix && <span className="kb-tab-suffix">{suffix}</span>}
                </ContextMenu>
              }
              key={item.path}
              closable={hasTabsMoreOne}
            >
              <Authorized
                authority={handleCheckAuthority.bind(null, item.path)}
                linkElement={Link}
                noMatch={
                  matchedRedirectPath ? (
                    <Redirect to={matchedRedirectPath} />
                  ) : (
                    <AuthorizedNoMatch />
                  )
                }
              >
                {childrenMap ? childrenMap[item.path] : null}
              </Authorized>
            </TabPane>
          );
        })}
      </Tabs>
    </div>
  );
};

export default PageTabs;
