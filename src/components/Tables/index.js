/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { PureComponent } from 'react';
import { Table } from 'antd';
import DragTable from '@/components/DragTable';
import { isNumber, isString } from 'lodash';

export default class Tables extends PureComponent {
  state = {};

  paginationChange = e => {
    this.props.paginationChange(e);
  };

  render() {
    const {
      dataSource,
      columns,
      bordered = true,
      loading,
      id,
      pagination = {},
      paginationChange,
      style,
      ...rest
    } = this.props;
    const {
      pageSize: pageSizeProps,
      size = 20,
      count,
      page = 1,
      customItemRender,
      ...restPagination
    } = pagination;
    const pageSize = pageSizeProps ? pageSizeProps : isNumber(size) ? size : 20;
    let paginationProp = {
      showQuickJumper: true,
      pageSize,
      total: count,
      defaultCurrent: 1,
      current: page,
      hideOnSinglePage: true,
      showTotal: () => `共 ${Math.ceil(count / pageSize)} 页`,
      itemRender: (current, type, originalElement) => {
        if (type === 'prev') {
          return <a style={{ marginRight: 5 }}>上一页</a>;
        }
        if (type === 'next') {
          return <a style={{ marginLeft: 5 }}>下一页</a>;
        }
        return originalElement;
      },
      size: isString(size) ? size : 'default',
      ...restPagination,
    };
    if (!customItemRender) {
      delete paginationProp.itemRender;
    }
    paginationProp = paginationChange
      ? { ...paginationProp, onChange: this.paginationChange }
      : paginationProp;
    return (
      <DragTable>
        <Table
          // locale={{ emptyText: '暂无数据' }}
          bordered={bordered}
          loading={loading}
          rowKey={id || 'id'}
          pagination={paginationProp}
          columns={columns}
          dataSource={dataSource}
          style={style}
          {...rest}
        />
      </DragTable>
    );
  }
}
