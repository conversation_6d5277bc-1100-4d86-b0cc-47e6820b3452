/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import ProForm, { ModalForm } from "@/components/AdaptForm"
import AdaptWrapperFn from "@/components/AdaptForm/form/AdaptWrapper"
import ProTable from "@/components/AdaptTable"
import React, { FC, useEffect, useState } from "react"
import { useColumns } from "./useColunms"
import { ModalSetFeeWrapper } from "./styles"
import { IListItem } from "./types"
interface IProps {
  trigger?: React.ReactElement;
  dataSource?: IListItem[],
  onFinish?: (values: any) => Promise<boolean>;
  title?: React.ReactNode;
}


const ModalSetFee: FC<IProps> = (props) => {
  const { trigger, dataSource, onFinish: propsOnFinish, title } = props
  const { columns } = useColumns()
  const [open, setOpen] = useState(false)
  const [form] = ProForm.useForm()
  const onFinish = async (values: any) => {
    const _values  = Object.fromEntries(
      Object.entries(values).filter(([_, v]) => v !== "" && v !== undefined)
    );
    const bool = await propsOnFinish(_values)
    return bool
  }
  useEffect(() => {
    if (open) {
      const record = {}
      dataSource.forEach(((item, index) => {
        record[item.brandKey] = item.fee
      }))
      form?.setFieldsValue(record)
    }

  }, [open])

  return <ModalSetFeeWrapper>
    <ModalForm
      onFinish={onFinish}
      onOpenChange={setOpen}
      open={open}
      form={form}
      trigger={trigger}
      title={title || '服务费设置'}
      style={{ height: 360, overflow: 'scroll' }}
      mountBody={false}
      >
      <ProTable dataSource={dataSource} columns={columns} pagination={false} search={false} />
    </ModalForm>
  </ModalSetFeeWrapper>
}

export default AdaptWrapperFn(ModalSetFee)
