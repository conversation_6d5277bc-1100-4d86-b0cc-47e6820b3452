/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable no-confusing-arrow */
/**
 * 分拣模式弹窗
 *  */
import React, { useEffect, useState, useCallback } from 'react';
import { Modal, Form, Select, Input, Row, Col, Spin, message } from 'antd';
import { connect } from 'dva';
import { getYdManagerInfo } from '@/services/automatic';
import { isObject } from '@turf/turf';
import TimeSelect from './timeSelect';
import ErrPrevention from './ErrPrevention/index';
import MatchSelect from './matchSelect';
import PreventError from './preventError';

const { Option } = Select;

const FormItem = Form.Item;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const canSelectBrand = ['ht', 'jt'];
const disabledSelectBrand = ['zt', 'yt'];

export const codeTypeOptions = [
  { label: '仅ocr', value: 'ocr', showErrPrevention: false },
  { label: '仅接口', value: 'third', showErrPrevention: true },
  { label: 'ocr优先', value: 'ocr_third', showErrPrevention: true },
  { label: '接口优先', value: 'third_ocr', showErrPrevention: true },
  { label: 'ocr与接口争先', value: 'third_ocr_fast', showErrPrevention: true, disabled: ['21'] },
];

const renderFormItems = ({
  token: initialValue = {},
  brands = [],
  brandList,
  sortingLineType,
  is_preventError,
}) => {
  /** 出港类型 */
  const outPort = sortingLineType === '2';
  /** 出港有效的品牌 */
  const enableBrand = brand => ['zt', 'ht', 'yt'].includes(brand);

  /** 中通和百世需要输入分拣线编号 */
  // const showInput = brand => ['zt', 'ht'].includes(brand);
  const showInput = () => false;
  // 去除分拣线编号 https://tower.im/teams/258300/todos/98903/

  // 含三段码显示匹配模式
  const showMatch = type => [1, 12, 21].includes(Number(type));

  /** 展示上线扫描选择 */
  const showScanType = (brand, type) => {
    // 中通特殊处理
    // if (brand === 'zt' && (type === '0' || !type)) {
    //   return false;
    // }
    if (disabledSelectBrand.includes(brand) && (type === '0' || outPort)) {
      return false;
    }
    if (canSelectBrand.includes(brand)) {
      return true;
    }
    return type !== '0';
  };

  const selectType = (fn, isOutPort) => {
    /** 分拣线类型 */
    const typeMap = {
      // 0: '标准分拣',
      1: '段码分拣',
      2: '地址分拣',
      12: '段码优先，地址补充',
      21: '地址优先，段码补充',
    };
    // 标准分拣品牌
    // const standModel = ['jt', 'yt'];
    // 出港只有标准分拣
    if (isOutPort) {
      typeMap[1] = undefined;
      typeMap[2] = undefined;
      typeMap[12] = undefined;
      typeMap[21] = undefined;
    }
    // if (!fn(brand) && !standModel.includes(brand)) {
    //   typeMap[0] = undefined;
    // }
    const arr = [];
    Object.keys(typeMap).forEach(key => {
      if (typeMap[key]) {
        arr.push({
          label: typeMap[key],
          value: key,
        });
      }
    });
    return arr;
  };

  const scanSelectType = () => {
    /** 上线扫描类型 */
    const scanType = {
      0: '不上传',
      1: '上传到件',
    };
    const arr = [];
    Object.keys(scanType).forEach(key => {
      if (scanType[key]) {
        arr.push({
          label: scanType[key],
          value: key,
        });
      }
    });
    return arr;
  };

  // 防错分 https://tower.im/teams/258300/todos/105370/

  const showErrPrevention = (brand, codeType, type) => {
    return (
      showMatch(type) &&
      (['yd'].includes(brand) || (['zt', 'sto'].includes(brand) && is_preventError)) &&
      codeTypeOptions.find(i => i.value === codeType).showErrPrevention
    );
  };
  const formItems = [];

  // 过滤全部品牌和全部格口
  brandList.filter(i => !['0', 'all'].includes(i.brand)).forEach(val => {
    const { brand, name } = val;
    const { type, key, isDao = '1', matchType = ['1'], get3CodeType, logistics, fourCode } =
      initialValue[brand] || {};
    const canSelect = brands.includes(brand);
    // 出港，只显示支持出港的品牌
    if (outPort && !enableBrand(brand)) return;
    formItems.push({
      brand,
      showScanType: showScanType(brand, type),
      showInput: showInput(brand),
      selectLabel: name,
      optionsArr: selectType(outPort ? enableBrand : showInput, outPort, brand),
      scanOptions: scanSelectType(),
      inputLabel: showInput(brand) && `${name}分拣线编号`,
      initialSelectValue: type || '1',
      initialInputValue: key || undefined,
      initialScanType: `${isDao}`, // 上线扫描类型
      canSelect,
      showMatch: showMatch(type || ((outPort ? enableBrand(brand) : showInput(brand)) ? '0' : '1')),
      showErrPrevention: showErrPrevention(
        brand,
        get3CodeType || 'ocr_third',
        type || ((outPort ? enableBrand(brand) : showInput(brand)) ? '0' : '1'),
      ),
      initialCodeType: get3CodeType || 'ocr_third',
      initialMatchType: Array.isArray(matchType) ? matchType : [`${matchType}`],
      initialLogistics: {
        type: logistics?.type || '0',
        time: logistics?.time ?? null,
        sTime: logistics?.sTime ?? null,
        eTime: logistics?.eTime ?? null,
      },
      initialFourCode: fourCode || '0',
    });
  });
  return formItems;
};

const SortingModelModal = ({
  visible,
  record,
  form,
  handleSubmit,
  onCancel,
  sortLineBrands,
  onSuccess,
  sortLineList,
  is_preventError,
}) => {
  const { getFieldDecorator, validateFields } = form;
  const {
    brand = '',
    sorting_line_token: sortLineToken = '',
    id,
    sorting_line_type: sortLineType,
  } = record;

  const [tokenVal, setTokenVal] = useState({});
  const [brands, setBrands] = useState([]);

  const getFormValues = (selectBrands = []) => {
    let sortingLineToken = {};
    validateFields((err, values) => {
      if (err) return;
      const formValues = {};
      Object.keys(values).forEach(key => {
        const [brandName] = key.split('_');
        // 只有被选中的品牌才能修改数据
        if (selectBrands.includes(brandName)) {
          formValues[brandName] = {
            type: values[brandName],
            key: values[`${brandName}_key`],
            isDao: values[`${brandName}_isDao`],
            matchType: values[`${brandName}_matchType`],
            get3CodeType: values[`${brandName}_codeType`],
            logistics: values[`${brandName}_logistics`],
          };
          if (is_preventError) {
            formValues[brandName] = {
              ...formValues[brandName],
              codeRelation: values[`${brandName}_codeRelation`],
              fourCode: values[`${brandName}_fourCode`],
            };
          }

          if (['yd'].includes(brandName)) {
            formValues[brandName] = {
              ...formValues[brandName],
              ydManager: values[`${brandName}_setting`],
            };
            if (!values[`${brandName}_setting`]) {
              delete formValues[brandName].ydManager;
            }
          }
          if (['zt', 'yt'].includes(brandName) && formValues[brandName].type === '0') {
            delete formValues[brandName].isDao;
          }
        }
      });
      sortingLineToken = formValues;
    });
    return sortingLineToken;
  };
  const currentRecord = sortLineList.find(i => i?.id == record?.id);
  const sorting_line_id = currentRecord?.id;
  const getYDManager = async () => {
    try {
      const { yd } = JSON.parse(currentRecord.sorting_line_token) || {};
      const empCode = yd?.ydManager?.empCode;
      const phone = yd?.ydManager?.phone;
      let item;
      if (empCode) {
        const { code, data } = await getYdManagerInfo({
          sorting_line_id,
          phone,
          emp_code: empCode,
        });
        if (code == 0 && isObject(data) && Object.keys(data).length) {
          item = data?.data;
        }
      }
      return item;
    } finally {
      //
    }
  };
  const onOk = useCallback(
    async (submitId, selectBrands) => {
      const v = getFormValues(selectBrands);
      const formValuesString = JSON.stringify(v);
      if (handleSubmit) {
        const brandsList = Object.keys(v);
        for (let index = 0; index < brandsList.length; index++) {
          const item = v[brandsList[index]];
          if (Array.isArray(item.matchType) && item.matchType.length == 0) {
            message.error('请选择匹配模式');
            return;
          }
        }
        if (formValuesString === '{}') {
          onCancel();
          return;
        }
        const { yd, ...rest } = JSON.parse(formValuesString);
        let str = formValuesString;
        if (yd && !yd.ydManager) {
          const item = await getYDManager();
          if (item) {
            const { empCode, phone } = item;
            const params = { ...yd, ydManager: { empCode, phone } };
            str = JSON.stringify({ ...rest, yd: params });
          }
        }

        handleSubmit({ sorting_line_token: str, id: submitId });
      }
      if (onCancel) {
        onCancel();
      }
    },
    [handleSubmit, onCancel, validateFields, record],
  );

  /**
   * 分拣类型选择
   *  */
  const onSelectChange = val => {
    const formValues = getFormValues(brands);

    const curBrandValues = formValues[val];

    if (
      curBrandValues &&
      codeTypeOptions
        .find(i => i.value == curBrandValues.get3CodeType)
        ?.disabled?.includes(curBrandValues.type)
    ) {
      curBrandValues.get3CodeType = 'ocr_third';
      form.setFieldsValue({ [`${val}_codeType`]: 'ocr_third' });
    }

    // 更新fromItem
    setTokenVal(formValues);
  };

  const onFourCodeChange = b => {
    form.setFieldsValue({ [`${b}_matchType`]: [] });
  };

  /**
   * codeTypeOptions
   */
  const onSelectCodeTypeChange = (val, _brand) => {
    const formValues = getFormValues(brands);
    setTokenVal({ ...formValues, brand: { ...formValues?.[_brand], get3CodeType: val } });
  };

  useEffect(
    () => {
      if (visible) {
        if (brand) {
          setBrands(brand.split(','));
        }
        console.info('sortLineToken=====>', sortLineToken);
        if (sortLineToken) {
          let token = {};
          try {
            token = JSON.parse(sortLineToken);
          } catch (error) {
            console.log('json字符串不完整');
          }
          setTokenVal(token);
        }
      } else {
        setBrands([]);
        setTokenVal({});
      }
    },
    [visible, brand, sortLineToken],
  );

  const widths = is_preventError
    ? [210, 140, 140, 150, 200, 200, 310]
    : [210, 0, 140, 150, 200, 200, 310];
  const totalWidth = widths.reduce((a, b) => a + b, 0) + 100;

  return (
    <Modal
      centered
      destroyOnClose
      title="分拣模式"
      visible={visible}
      onOk={() => onOk(id, brands)}
      onCancel={onCancel}
      width={totalWidth}
    >
      <Form {...formItemLayout}>
        {renderFormItems({
          token: tokenVal,
          brands,
          brandList: sortLineBrands,
          sortingLineType: sortLineType,
          is_preventError,
        }).map(val => (
          <Row key={val.brand} type="flex" gutter={[12, 0]}>
            <Col style={{ width: widths[0] }}>
              <FormItem label={val.selectLabel}>
                {getFieldDecorator(val.brand, {
                  initialValue: val.initialSelectValue,
                })(
                  <Select onSelect={() => onSelectChange(val.brand)} disabled={!val.canSelect}>
                    {val.optionsArr.map(({ value, label }) => (
                      <Option key={value} value={value}>
                        {label}
                      </Option>
                    ))}
                  </Select>,
                )}
              </FormItem>
            </Col>
            <Col style={{ width: widths[1] }}>
              {val.showMatch &&
                is_preventError && (
                  <FormItem wrapperCol={{ span: 24 }}>
                    {getFieldDecorator(`${val.brand}_fourCode`, {
                      initialValue: val.initialFourCode,
                    })(
                      <Select
                        onSelect={onSelectChange}
                        onChange={() => onFourCodeChange(val.brand)}
                        disabled={!val.canSelect}
                      >
                        <Option value="0">不分拣四段码</Option>
                        <Option value="1">分拣四段码</Option>
                      </Select>,
                    )}
                  </FormItem>
                )}
            </Col>
            <Col style={{ width: widths[2] }}>
              {val.showMatch && (
                <FormItem wrapperCol={{ span: 24 }}>
                  {getFieldDecorator(`${val.brand}_codeType`, {
                    initialValue: val.initialCodeType,
                  })(
                    <Select
                      disabled={!val.canSelect}
                      onSelect={value => onSelectCodeTypeChange(value, val.brand)}
                    >
                      {codeTypeOptions
                        .filter(i => !i.disabled?.includes(val.initialSelectValue))
                        .map(({ value, label }) => (
                          <Option key={value} value={value}>
                            {label}
                          </Option>
                        ))}
                    </Select>,
                  )}
                </FormItem>
              )}
            </Col>
            <Col style={{ width: widths[3] }}>
              {val.showErrPrevention && (
                <FormItem labelCol={{ span: 10 }} wrapperCol={{ span: 14 }} label="防错分">
                  {val.brand === 'zt' || val.brand == 'sto'
                    ? getFieldDecorator(`${val.brand}_codeRelation`)(
                        <PreventError
                          brand={val.brand}
                          brandName={val.selectLabel}
                          disabled={!val.canSelect}
                          record={record}
                          tokenVal={tokenVal}
                          handleSetToken={handleSubmit}
                          setTokenVal={setTokenVal}
                        />,
                      )
                    : getFieldDecorator(`${val.brand}_setting`)(
                        <ErrPrevention
                          record={record}
                          onSuccess={onSuccess}
                          trigger={
                            val.showErrPrevention
                              ? ({ value, loading }) =>
                                  loading ? (
                                    <Spin spinning={loading} />
                                  ) : (
                                    <Input
                                      disabled={!val.canSelect}
                                      style={{ cursor: 'pointer' }}
                                      readOnly
                                      value={value == 1 ? '未设置' : '已设置'}
                                    />
                                  )
                              : null
                          }
                        />,
                      )}
                </FormItem>
              )}
            </Col>
            <Col style={{ width: widths[4] }}>
              <FormItem label="上线扫描" labelCol={{ span: 10 }} wrapperCol={{ span: 14 }}>
                {getFieldDecorator(`${val.brand}_isDao`, {
                  initialValue: val.initialScanType,
                })(
                  <Select disabled={!val.canSelect || !val.showScanType}>
                    {val.scanOptions.map(({ value, label }) => (
                      <Option key={value} value={value}>
                        {label}
                      </Option>
                    ))}
                  </Select>,
                )}
              </FormItem>
            </Col>
            <Col style={{ width: widths[5] }}>
              <FormItem label="下线派发" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
                {getFieldDecorator(`${val.brand}_logistics`, {
                  initialValue: val.initialLogistics,
                })(<TimeSelect disabled={!val.canSelect} />)}
              </FormItem>
            </Col>
            {/* <Col>
              {val.showInput && (
                <FormItem label={val.inputLabel} labelCol={{ span: 9 }} wrapperCol={{ span: 13 }}>
                  {getFieldDecorator(`${val.brand}_key`, {
                    initialValue: val.initialInputValue,
                    rules: [
                      {
                        max: 40,
                        message: '名称最长不超过40位字符',
                      },
                    ],
                  })(
                    <Input
                      disabled={!val.canSelect}
                      placeholder={`请输入${val.selectLabel}分拣线编号`}
                    />,
                  )}
                </FormItem>
              )}
            </Col> */}
            <Col style={{ width: widths[6] }}>
              {val.showMatch && (
                <FormItem label="匹配模式" labelCol={{ span: 6 }} wrapperCol={{ span: 17 }}>
                  {getFieldDecorator(`${val.brand}_matchType`, {
                    initialValue: val.initialMatchType,
                  })(<MatchSelect type={val.initialFourCode} disabled={!val.canSelect} />)}
                </FormItem>
              )}
            </Col>
          </Row>
        ))}
      </Form>
    </Modal>
  );
};

export default Form.create()(
  connect(({ automatic, user }) => {
    const sortLineBrands = automatic.sortLineBrands.filter(
      b => !['fw', 'zykd', 'ht'].includes(b.brand),
    );
    return {
      sortLineBrands,
      sortLineList: automatic?.sortLineList?.list || [],
      is_preventError: user.currentUser.is_preventError,
    };
  })(SortingModelModal),
);
