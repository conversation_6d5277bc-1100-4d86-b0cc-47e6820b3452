/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { isLegalData } from '@/utils/utils';
import {
  getSortLineList,
  deleteSortLineList,
  addSortLine,
  getAuthCodeList,
  cancelAuthCode,
  addAuthCode,
  getGridConfList,
  deleteGridConfig,
  getInnNum,
  addGrid,
  editGrid,
  getArrivalSortingList,
  getGridStatisticsList,
  checkCode,
  addAbnormalGrid,
  getScanner,
  getGridSortLineList,
  getSubOperator,
  getSortLineBrands,
  saveModel,
  dispatchConfig,
  getAddressSortList,
  getAllGrid,
} from '@/services/automatic';
import { getBranchInfo } from '@/services/configuration';
import { message } from 'antd';
import moment from 'moment';

// 验证查询时间范围是否合法
const validateTimeRange = (startTime, endTime, isExport = false) => {
  if (!startTime || !endTime) return true;

  const separationDate = moment('2025-01-21');
  const startMoment = moment(startTime);
  const endMoment = moment(endTime);

  // 2025-01-21前：限制只能查询同一月份数据
  if (startMoment.isBefore(separationDate)) {
    if (startMoment.format('YYYY-MM') !== endMoment.format('YYYY-MM')) {
      message.warn(`不允许跨月${isExport ? '导出' : '查询'}！`);
      return false;
    }
  } else {
    // 2025-01-21后：离当前62天之内的可以跨月，否则也只能查询同一月份
    const daysDiff = endMoment.diff(startMoment, 'days');
    if (daysDiff > 62) {
      if (startMoment.format('YYYY-MM') !== endMoment.format('YYYY-MM')) {
        message.warn(`超过62天的数据不允许跨月${isExport ? '导出' : '查询'}！`);
        return false;
      }
    }
  }
  return true;
};

export default {
  namespace: 'automatic',

  state: {
    sortLineList: {
      list: [],
      pagination: {},
    },
    authCodeList: [],
    gridConfList: {
      list: [],
      pagination: {},
    },
    arrivalSortingList: {
      list: [],
      pagination: {},
    },
    gridStatisticsList: {
      list: [],
      pagination: {},
    },
    addressSortList: {
      list: [],
      pagination: {},
    },
    upDownScanner: [],
    gridSortLineList: [],
    sortLineBrands: [],
    abnormalGridNameOptions: {},
  },

  effects: {
    // 分拣线配置，获取列表
    *getSortLineList({ payload, __dva_resolve: resolve, __dva_reject: reject }, { call, put }) {
      const response = yield call(getSortLineList, payload);
      if (!response) return;
      const { code, data, msg } = response;
      if (code === 0 && data) {
        yield put({
          type: 'save',
          payload: {
            sortLineList: {
              list: isLegalData(data, []),
            },
          },
        });
        resolve({
          list: isLegalData(data, []),
          pagination: {},
        });
      } else {
        reject();
        message.error(msg);
      }
    },
    // 分拣线配置，获取列表
    *getSortLineBrands({ payload, __dva_resolve: resolve, __dva_reject: reject }, { call, put }) {
      const response = yield call(getSortLineBrands, payload);
      if (!response) return;
      const { code, data, msg } = response;
      const brands = [
        {
          name: '全部格口',
          brand: '0',
        },
        ...isLegalData(data, []),
      ];
      if (code === 0 && data) {
        yield put({
          type: 'save',
          payload: {
            sortLineBrands: brands,
          },
        });
        resolve(brands);
      } else {
        reject();
        message.error(msg);
      }
    },
    // 分拣线配置，删除列表
    *deleteSortLineList({ payload, __dva_resolve: resolve, __dva_reject: reject }, { call }) {
      const response = yield call(deleteSortLineList, payload);
      if (!response) return;
      const { code, msg } = response;
      if (code === 0) {
        resolve(response);
      } else {
        reject(response);
        message.error(msg);
      }
    },
    // 分拣线配置，新增分拣线
    *addSortLine({ payload, __dva_resolve: resolve, __dva_reject: reject }, { call }) {
      const response = yield call(addSortLine, payload);
      if (!response) return;
      const { code, msg } = response;
      if (code === 0) {
        resolve(response);
      } else {
        reject();
        message.error(msg);
      }
    },
    // 分拣线配置，授权码列表
    *getAuthCodeList({ payload, __dva_resolve: resolve, __dva_reject: reject }, { call, put }) {
      const response = yield call(getAuthCodeList, payload);
      if (!response) return;
      const { code, data, msg } = response;
      if (code === 0 && data) {
        yield put({
          type: 'save',
          payload: {
            authCodeList: isLegalData(data, []),
          },
        });
        resolve(isLegalData(data, []));
      } else {
        reject();
        message.error(msg);
      }
    },
    // 分拣线配置，取消授权
    *cancelAuthCode({ payload, __dva_resolve: resolve, __dva_reject: reject }, { call }) {
      const response = yield call(cancelAuthCode, payload);
      if (!response) return;
      const { code, msg } = response;
      if (code === 0) {
        resolve(response);
        message.success(msg);
      } else {
        reject(response);
        message.error(msg);
      }
    },
    // 分拣线配置，添加授权码
    *addAuthCode({ payload, __dva_resolve: resolve, __dva_reject: reject }, { call }) {
      const response = yield call(addAuthCode, payload);
      if (!response) return;
      const { code, msg } = response;
      if (code === 0) {
        resolve('获取成功！');
        message.success(msg);
      } else {
        reject(response);
        message.error(msg);
      }
    },
    // 分拣线配置，校验分拣线编号
    *checkCode({ payload, __dva_resolve: resolve }, { call }) {
      const response = yield call(checkCode, payload);
      if (!response) return;
      const { code, msg, data } = response;
      if (code === 0) {
        resolve({ passed: data === 0 || data === '0', msg: '编号已存在' });
      } else {
        message.error(msg);
        resolve({ passed: false, msg });
      }
    },
    // 分拣线配置，获取上线下线扫描员
    *getScanner({ payload, __dva_resolve: resolve, __dva_reject: reject }, { call, put }) {
      const response = yield call(getScanner, payload);
      if (!response) return;
      const { code, msg, data } = response;

      if (code === 0) {
        yield put({
          type: 'save',
          payload: {
            upDownScanner: isLegalData(data),
          },
        });
        resolve(isLegalData(data));
      } else {
        message.error(msg);
        reject(response);
      }
    },
    // 分拣线配置，删除列表
    *deleteGridConfig({ payload, __dva_resolve: resolve }, { call }) {
      const response = yield call(deleteGridConfig, payload);
      if (!response) return;
      resolve(response);
    },
    // 分拣线配置，设置分拣模式
    *saveModel({ payload, __dva_resolve: resolve, __dva_reject: reject }, { call }) {
      const response = yield call(saveModel, payload);
      if (!response) return;
      const { code, msg } = response;
      if (code === 0) {
        message.success('设置成功！');
        resolve(response);
      } else {
        message.error(msg);
        reject(response);
      }
    },

    // 格口配置，获取格口配置列表
    *getGridConfList({ payload, __dva_resolve: resolve, __dva_reject: reject }, { call }) {
      const response = yield call(getGridConfList, payload);
      if (!response) return;
      const { code, data = {}, msg } = response;
      const { list, page, pageSize, count, eq } = data;
      if (code === 0 && data) {
        const { grid_code_or_name } = payload;
        resolve({
          list: isLegalData(list),
          pagination: {
            total: count * 1,
            pageSize: pageSize * 1,
            current: page * 1,
            showQuickJumper: false,
            showSizeChanger: false,
          },
          eq,
          grid_code_or_name,
        });
      } else {
        reject();
        message.error(msg);
      }
    },
    // 格口配置，获取网点编号
    *getBranchInfo({ payload, __dva_resolve: resolve, __dva_reject: reject }, { call }) {
      const response = yield call(getBranchInfo, payload);
      const { brand } = payload;
      if (!response) return;
      const { code, data = {}, msg } = response;
      if (code === 0 && data) {
        let arr = data[brand];
        if (brand == 'all') {
          let brandCode = [];
          Object.keys(data).forEach(item => {
            brandCode = Array.from(new Set([...brandCode, ...data[item]]));
          });
          arr = brandCode;
        }
        resolve(arr);
      } else {
        reject();
        message.error(msg);
      }
    },
    // 格口配置，获取分拣线下拉数据
    *getGridSortLineList({ payload, __dva_resolve: resolve, __dva_reject: reject }, { call, put }) {
      const response = yield call(getGridSortLineList, payload);
      if (!response) return;
      const { code, data = [], msg } = response;
      if (code === 0 && data) {
        yield put({
          type: 'save',
          payload: {
            gridSortLineList: isLegalData(data),
          },
        });
        resolve(isLegalData(data));
      } else {
        reject();
        message.error(msg);
      }
    },
    // 格口配置，新增正常格口
    *addGrid({ payload, __dva_resolve: resolve }, { call }) {
      const response = yield call(addGrid, payload);
      if (!response) return;
      resolve(response);
    },
    // 格口配置，新增异常格口
    *addAbnormalGrid({ payload, __dva_resolve: resolve }, { call }) {
      const response = yield call(addAbnormalGrid, payload);
      if (!response) return;
      resolve(response);
    },
    // 格口配置，编辑异常、正常格口
    *editGrid({ payload, __dva_resolve: resolve }, { call }) {
      const response = yield call(editGrid, payload);
      if (!response) return;
      resolve(response);
    },
    // 格口配置，新增异常、正常格口，网点编号查询
    *getInnNum({ payload, __dva_resolve: resolve, __dva_reject: reject }, { call }) {
      const response = yield call(getInnNum, payload);
      const { msg, data, code } = response;
      if (code === 0) {
        resolve(data);
      } else {
        reject(response);
        message.error(msg);
      }
    },
    // 格口配置，新增正常格口，获取下节点操作对象
    *getSubOperator({ payload, __dva_resolve: resolve, __dva_reject: reject }, { call }) {
      const response = yield call(getSubOperator, payload);
      const { msg, data, code } = response;
      if (code === 0) {
        resolve(isLegalData(data));
      } else {
        reject(response);
        message.error(msg);
      }
    },
    // 进港分拣，获取列表
    *getArrivalSortingList({ payload, __dva_resolve: resolve, __dva_reject: reject }, { call }) {
      const { scan_time_start, scan_time_end, down } = payload;

      // 验证时间范围
      if (!validateTimeRange(scan_time_start, scan_time_end, down)) {
        reject();
        return;
      }

      const response = yield call(getArrivalSortingList, payload);
      if (!response) return;
      const { code, data = {}, msg } = response;
      const { list, page, pageSize, count } = data;
      if (code === 0 && data) {
        resolve({
          list: isLegalData(list),
          pagination: {
            total: count * 1,
            pageSize: pageSize * 1,
            current: page * 1,
            showQuickJumper: false,
            showSizeChanger: false,
          },
        });
      } else {
        reject();
        message.error(msg);
      }
    },
    // 格口统计，获取列表
    *getGridStatisticsList({ payload, __dva_resolve: resolve, __dva_reject: reject }, { call }) {
      const response = yield call(getGridStatisticsList, payload);
      if (!response) return;
      const { code, data = {}, msg } = response;
      const { list, page, pageSize, count } = data;
      if (code === 0 && data) {
        resolve({
          list: isLegalData(list),
          pagination: {
            total: count * 1,
            pageSize: pageSize * 1,
            current: page * 1,
            showQuickJumper: false,
            showSizeChanger: false,
          },
        });
      } else {
        reject();
        message.error(msg);
      }
    },
    // 格口统计，下发配置
    *dispatchConfig({ payload, __dva_resolve, __dva_reject }, { call }) {
      const response = yield call(dispatchConfig, payload);
      if (!response) return;
      const { code, msg } = response;
      if (code === 0) {
        __dva_resolve();
      } else {
        __dva_reject(msg);
      }
    },
    // 分拣线配置，地址分拣列表
    *getAddressSortList({ payload, __dva_resolve: resolve, __dva_reject: reject }, { call }) {
      const response = yield call(getAddressSortList, payload);
      if (!response) return;
      const { code, data, msg } = response;
      const { list, total, page, size } = data || {};
      if (code === 0 && list) {
        resolve({
          list: isLegalData(list),
          pagination: {
            total,
            page,
            pageSize: size,
          },
        });
      } else {
        reject();
        message.error(msg);
      }
    },
    *getAbnormalGridNameOptions(_, { call, put }) {
      const response = yield call(getAllGrid);
      if (!response) return;
      const { data } = response;
      yield put({
        type: 'save',
        payload: {
          abnormalGridNameOptions: data,
        },
      });
    },
  },
  reducers: {
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
